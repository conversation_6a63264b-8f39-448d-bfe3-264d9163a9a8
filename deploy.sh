#!/bin/bash

# Appwrite Deployment Script for Azure VPS
# Domain: taskhub.me
# IP: *************

echo "🚀 Starting Appwrite deployment for taskhub.me..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

# Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install Docker if not installed
if ! command -v docker &> /dev/null; then
    print_status "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl enable docker
    systemctl start docker
    rm get-docker.sh
else
    print_status "Docker is already installed"
fi

# Install Docker Compose if not installed
if ! command -v docker-compose &> /dev/null; then
    print_status "Installing Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
else
    print_status "Docker Compose is already installed"
fi

# Create appwrite directory
print_status "Creating Appwrite directory..."
mkdir -p /opt/appwrite
cd /opt/appwrite

# Copy files (assuming they're in the current directory)
if [ -f "docker-compose.yml" ] && [ -f ".env" ]; then
    print_status "Copying configuration files..."
    cp docker-compose.yml /opt/appwrite/
    cp .env /opt/appwrite/
else
    print_error "docker-compose.yml or .env file not found in current directory"
    print_error "Please ensure these files are in the same directory as this script"
    exit 1
fi

# Generate secure passwords and keys
print_status "Generating secure passwords and keys..."

# Generate random passwords
DB_ROOT_PASS=$(openssl rand -base64 32)
DB_PASS=$(openssl rand -base64 32)
OPENSSL_KEY=$(openssl rand -base64 32)
EXECUTOR_SECRET=$(openssl rand -base64 32)

# Update .env file with generated passwords
sed -i "s/your-secure-db-password-change-this/$DB_PASS/g" .env
sed -i "s/your-secure-root-password-change-this/$DB_ROOT_PASS/g" .env
sed -i "s/your-secret-key-here-change-this-to-something-secure-32-chars-minimum/$OPENSSL_KEY/g" .env
sed -i "s/your-executor-secret-change-this-to-something-secure/$EXECUTOR_SECRET/g" .env

print_status "Generated secure passwords and updated .env file"

# Create necessary directories and set permissions
print_status "Creating directories and setting permissions..."
mkdir -p /opt/appwrite/storage/{uploads,cache,config,certificates,functions,builds,sites,imports}
chmod -R 755 /opt/appwrite/storage

# Configure firewall (UFW)
print_status "Configuring firewall..."
ufw --force enable
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw reload

# Create Docker network
print_status "Creating Docker networks..."
docker network create gateway 2>/dev/null || true
docker network create appwrite 2>/dev/null || true
docker network create runtimes 2>/dev/null || true

# Create Docker volumes
print_status "Creating Docker volumes..."
docker volume create appwrite-mariadb 2>/dev/null || true
docker volume create appwrite-redis 2>/dev/null || true
docker volume create appwrite-cache 2>/dev/null || true
docker volume create appwrite-uploads 2>/dev/null || true
docker volume create appwrite-imports 2>/dev/null || true
docker volume create appwrite-certificates 2>/dev/null || true
docker volume create appwrite-functions 2>/dev/null || true
docker volume create appwrite-sites 2>/dev/null || true
docker volume create appwrite-builds 2>/dev/null || true
docker volume create appwrite-config 2>/dev/null || true

# Start MariaDB
print_status "Starting MariaDB..."
docker run -d \
  --name appwrite-mariadb \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-mariadb:/var/lib/mysql:rw \
  -e MYSQL_ROOT_PASSWORD=$DB_ROOT_PASS \
  -e MYSQL_DATABASE=appwrite \
  -e MYSQL_USER=user \
  -e MYSQL_PASSWORD=$DB_PASS \
  -e MARIADB_AUTO_UPGRADE=1 \
  mariadb:10.11 \
  mysqld --innodb-flush-method=fsync

# Start Redis
print_status "Starting Redis..."
docker run -d \
  --name appwrite-redis \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-redis:/data:rw \
  redis:7.2.4-alpine \
  redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru --maxmemory-samples 5

# Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 30

# Start OpenRuntimes Executor
print_status "Starting OpenRuntimes Executor..."
docker run -d \
  --name openruntimes-executor \
  --hostname exc1 \
  --network appwrite \
  --restart unless-stopped \
  --stop-signal SIGINT \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v appwrite-builds:/storage/builds:rw \
  -v appwrite-functions:/storage/functions:rw \
  -v appwrite-sites:/storage/sites:rw \
  -v /tmp:/tmp:rw \
  --env-file .env \
  openruntimes/executor:0.7.14

# Start Appwrite main service
print_status "Starting Appwrite main service..."
docker run -d \
  --name appwrite \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-uploads:/storage/uploads:rw \
  -v appwrite-imports:/storage/imports:rw \
  -v appwrite-cache:/storage/cache:rw \
  -v appwrite-config:/storage/config:rw \
  -v appwrite-certificates:/storage/certificates:rw \
  -v appwrite-functions:/storage/functions:rw \
  -v appwrite-sites:/storage/sites:rw \
  -v appwrite-builds:/storage/builds:rw \
  --env-file .env \
  appwrite/appwrite:1.7.3

# Start Appwrite Console
print_status "Starting Appwrite Console..."
docker run -d \
  --name appwrite-console \
  --network appwrite \
  --restart unless-stopped \
  appwrite/console:6.0.11

# Start Traefik (Reverse Proxy)
print_status "Starting Traefik reverse proxy..."
docker run -d \
  --name appwrite-traefik \
  --network gateway \
  --network appwrite \
  --restart unless-stopped \
  -p 80:80 \
  -p 443:443 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v appwrite-config:/storage/config:rw \
  -v appwrite-certificates:/storage/certificates:rw \
  traefik:2.11 \
  --providers.file.directory=/storage/config \
  --providers.file.watch=true \
  --providers.docker=true \
  --providers.docker.exposedByDefault=false \
  --providers.docker.constraints=Label\(\`traefik.constraint-label-stack\`,\`appwrite\`\) \
  --entrypoints.appwrite_web.address=:80 \
  --entrypoints.appwrite_websecure.address=:443 \
  --certificatesresolvers.letsencrypt.acme.httpchallenge=true \
  --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=appwrite_web \
  --certificatesresolvers.letsencrypt.acme.email=<EMAIL> \
  --certificatesresolvers.letsencrypt.acme.storage=/storage/acme.json \
  --entrypoints.appwrite_web.http.redirections.entrypoint.to=appwrite_websecure \
  --entrypoints.appwrite_web.http.redirections.entrypoint.scheme=https

# Wait for services to start
print_status "Waiting for services to start..."
sleep 30

# Check if services are running
print_status "Checking service status..."
docker ps --filter "name=appwrite"

# Display important information
echo ""
echo "🎉 Appwrite deployment completed!"
echo ""
echo "📋 Important Information:"
echo "========================"
echo "🌐 Domain: https://taskhub.me"
echo "🖥️  Console: https://taskhub.me/console"
echo "📧 Admin Email: <EMAIL> (change this in DNS/email setup)"
echo ""
echo "🔐 Generated Passwords (SAVE THESE SECURELY):"
echo "Database Root Password: $DB_ROOT_PASS"
echo "Database User Password: $DB_PASS"
echo "OpenSSL Key: $OPENSSL_KEY"
echo "Executor Secret: $EXECUTOR_SECRET"
echo ""
echo "📝 Next Steps:"
echo "1. Point your domain taskhub.me to this server IP: *************"
echo "2. Wait for DNS propagation (can take up to 48 hours)"
echo "3. Configure email settings in .env file if needed"
echo "4. Access https://taskhub.me/console to set up your first project"
echo ""
echo "🔧 Useful Commands:"
echo "View logs: docker logs -f appwrite"
echo "View all containers: docker ps --filter 'name=appwrite'"
echo "Restart Appwrite: docker restart appwrite"
echo "Stop all services: docker stop \$(docker ps -q --filter 'name=appwrite')"
echo "Update Appwrite: docker pull appwrite/appwrite:1.7.3 && docker restart appwrite"
echo ""
print_warning "Make sure to:"
print_warning "1. Save the generated passwords securely"
print_warning "2. Configure your domain DNS to point to *************"
print_warning "3. Set up email SMTP settings in .env if you need email functionality"
