# Appwrite Deployment Script for Azure VPS (PowerShell)
# Domain: taskhub.me
# IP: *************

Write-Host "🚀 Starting Appwrite deployment for taskhub.me..." -ForegroundColor Green

# Function to generate random password
function Generate-RandomPassword {
    param([int]$Length = 32)
    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
    $password = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $password += $chars[(Get-Random -Maximum $chars.Length)]
    }
    return $password
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "❌ .env file not found!" -ForegroundColor Red
    Write-Host "Please make sure the .env file is in the current directory." -ForegroundColor Red
    exit 1
}

# Generate secure passwords
Write-Host "🔐 Generating secure passwords..." -ForegroundColor Yellow

$dbRootPass = Generate-RandomPassword -Length 32
$dbPass = Generate-RandomPassword -Length 32
$opensslKey = Generate-RandomPassword -Length 32
$executorSecret = Generate-RandomPassword -Length 32

# Read .env file content
$envContent = Get-Content ".env" -Raw

# Replace placeholder passwords with generated ones
$envContent = $envContent -replace "your-secure-db-password-change-this", $dbPass
$envContent = $envContent -replace "your-secure-root-password-change-this", $dbRootPass
$envContent = $envContent -replace "your-secret-key-here-change-this-to-something-secure-32-chars-minimum", $opensslKey
$envContent = $envContent -replace "your-executor-secret-change-this-to-something-secure", $executorSecret

# Write updated content back to .env file
Set-Content ".env" -Value $envContent

Write-Host "✅ Updated .env file with secure passwords" -ForegroundColor Green

# Display deployment instructions
Write-Host ""
Write-Host "📋 Deployment Instructions for Azure VPS" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 📤 Upload files to your Azure VPS:" -ForegroundColor White
Write-Host "   - Copy docker-compose.yml to your VPS" -ForegroundColor Gray
Write-Host "   - Copy .env to your VPS" -ForegroundColor Gray
Write-Host "   - Copy deploy.sh to your VPS" -ForegroundColor Gray
Write-Host ""
Write-Host "2. 🔗 Connect to your Azure VPS:" -ForegroundColor White
Write-Host "   ssh username@*************" -ForegroundColor Gray
Write-Host ""
Write-Host "3. 🚀 Run the deployment script:" -ForegroundColor White
Write-Host "   chmod +x deploy.sh" -ForegroundColor Gray
Write-Host "   sudo ./deploy.sh" -ForegroundColor Gray
Write-Host ""
Write-Host "4. 🌐 Configure DNS:" -ForegroundColor White
Write-Host "   Point taskhub.me to *************" -ForegroundColor Gray
Write-Host "   Add A record: @ -> *************" -ForegroundColor Gray
Write-Host "   Add A record: www -> *************" -ForegroundColor Gray
Write-Host ""

# Display generated passwords
Write-Host "🔐 Generated Passwords (SAVE THESE SECURELY):" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Red
Write-Host "Database Root Password: $dbRootPass" -ForegroundColor Yellow
Write-Host "Database User Password: $dbPass" -ForegroundColor Yellow
Write-Host "OpenSSL Key: $opensslKey" -ForegroundColor Yellow
Write-Host "Executor Secret: $executorSecret" -ForegroundColor Yellow
Write-Host ""

# Save passwords to a file
$passwordInfo = @"
Appwrite Deployment Passwords for taskhub.me
Generated on: $(Get-Date)
Server IP: *************

Database Root Password: $dbRootPass
Database User Password: $dbPass
OpenSSL Key: $opensslKey
Executor Secret: $executorSecret

IMPORTANT: Keep this file secure and delete it after saving passwords elsewhere!
"@

Set-Content "appwrite-passwords.txt" -Value $passwordInfo
Write-Host "💾 Passwords saved to appwrite-passwords.txt" -ForegroundColor Green
Write-Host ""

# Display next steps
Write-Host "📝 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Upload the files to your Azure VPS" -ForegroundColor White
Write-Host "2. Run the deployment script on your VPS" -ForegroundColor White
Write-Host "3. Configure DNS for taskhub.me" -ForegroundColor White
Write-Host "4. Wait for SSL certificate generation" -ForegroundColor White
Write-Host "5. Access https://taskhub.me/console" -ForegroundColor White
Write-Host ""

# Display file upload commands
Write-Host "📤 File Upload Commands (using SCP):" -ForegroundColor Cyan
Write-Host "scp docker-compose.yml username@*************:~/" -ForegroundColor Gray
Write-Host "scp .env username@*************:~/" -ForegroundColor Gray
Write-Host "scp deploy.sh username@*************:~/" -ForegroundColor Gray
Write-Host ""

Write-Host "✅ Local preparation completed!" -ForegroundColor Green
Write-Host "Now upload the files to your VPS and run the deployment script." -ForegroundColor White
