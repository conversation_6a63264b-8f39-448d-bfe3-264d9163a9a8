import librosa
import librosa.display
import matplotlib.pyplot as plt
import numpy as np
import os # For checking file existence

# --- Configuration ---
# <--- IMPORTANT: Change this to the actual path of your audio file! --->
AUDIO_FILE = 'bura_din.mp3'

# --- 0. Pre-check and Load Audio ---
print(f"--- Starting Audio Analysis for: {AUDIO_FILE} ---")

if not os.path.exists(AUDIO_FILE):
    print(f"Error: Audio file not found at '{AUDIO_FILE}'.")
    print("Please ensure the file exists and the path is correct.")
    exit()

try:
    # y: audio time series, sr: sampling rate of y
    # Load at original sampling rate for accurate waveform/spectrograms
    y, sr = librosa.load(AUDIO_FILE, sr=None)
    print(f"Successfully loaded '{AUDIO_FILE}'")
    print(f"Sampling Rate (sr): {sr} Hz")
    print(f"Duration: {librosa.get_duration(y=y, sr=sr):.2f} seconds")
    print(f"Number of samples: {len(y)}")

except Exception as e:
    print(f"An error occurred while loading the audio: {e}")
    exit()

# --- 1. Basic Audio Loading and Waveform/RMS Visualization ---
print("\n--- 1. Waveform and RMS Analysis ---")

# Waveform Visualization
plt.figure(figsize=(12, 6))

plt.subplot(2, 1, 1) # 2 rows, 1 column, 1st plot
librosa.display.waveshow(y=y, sr=sr, x_axis='time', color='purple')
plt.title('Audio Waveform')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.grid(True)

# Basic Amplitude Analysis (RMS)
rms = librosa.feature.rms(y=y)[0]
times = librosa.times_like(rms, sr=sr)

plt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot
plt.plot(times, rms, label='RMS Energy', color='teal')
plt.title('RMS Energy Over Time (Loudness Indicator)')
plt.xlabel('Time (s)')
plt.ylabel('RMS Value')
plt.grid(True)
plt.legend()

plt.tight_layout()
plt.show()

# You can also get a single average RMS value for the whole track
avg_rms = np.mean(rms)
print(f"Average RMS Energy: {avg_rms:.4f}")


# --- 2. Frequency Analysis: Spectrograms ---
print("\n--- 2. Frequency Analysis (Spectrograms) ---")

# Compute Spectrogram (Short-Time Fourier Transform)
D = librosa.stft(y)
S_db = librosa.amplitude_to_db(np.abs(D), ref=np.max)

plt.figure(figsize=(12, 10))

plt.subplot(2, 1, 1) # 2 rows, 1 column, 1st plot
librosa.display.specshow(S_db, sr=sr, x_axis='time', y_axis='hz', cmap='viridis')
plt.colorbar(format='%+2.0f dB')
plt.title('Spectrogram (Frequency vs. Time)')
plt.xlabel('Time (s)')
plt.ylabel('Frequency (Hz)')

# Compute Mel-Spectrogram (more perceptually relevant)
M = librosa.feature.melspectrogram(y=y, sr=sr)
M_db = librosa.amplitude_to_db(M, ref=np.max)

plt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot
librosa.display.specshow(M_db, sr=sr, x_axis='time', y_axis='mel', cmap='magma')
plt.colorbar(format='%+2.0f dB')
plt.title('Mel-Spectrogram')
plt.xlabel('Time (s)')
plt.ylabel('Mel Frequency')

plt.tight_layout()
plt.show()

# --- 3. Musical Feature Analysis: Tempo (BPM) and Key Detection ---
print("\n--- 3. Musical Feature Analysis (Tempo & Key) ---")

# For tempo and key, we often downsample slightly for faster/more robust processing
# This creates a separate `y_features` variable to avoid altering the original `y`
sr_features = 22050
y_features, _ = librosa.load(AUDIO_FILE, sr=sr_features) # Load again with specific SR for features

# Tempo (BPM) Estimation
tempo, beats = librosa.beat.beat_track(y=y_features, sr=sr_features)
# FIX: Access the first element of the tempo array
print(f"Estimated Tempo: {tempo[0]:.2f} BPM")

# Key Detection
# First, separate harmonic components for better key estimation
y_harmonic, y_percussive = librosa.effects.hpss(y_features)
# Compute Chroma features on the harmonic part
chroma_harmonic = librosa.feature.chroma_cqt(y=y_harmonic, sr=sr_features)

# Define Major and Minor chord templates (simplified for demonstration)
# These represent the relative strength of each pitch class in a given key
major_template = np.array([6.35, 2.23, 3.48, 2.33, 4.38, 4.09, 2.52, 5.19, 2.39, 3.66, 2.29, 2.88])
minor_template = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])

# Normalize templates to sum to 1 for better correlation comparison
major_template /= np.sum(major_template)
minor_template /= np.sum(minor_template)

# Calculate the average chroma vector for the track
avg_chroma = np.mean(chroma_harmonic, axis=1)
# Normalize the average chroma vector
avg_chroma /= np.sum(avg_chroma)

# Function to correlate chroma with key templates
def estimate_key_from_chroma(avg_chroma, sr_features):
    keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
    key_candidates = []

    for i in range(12): # Iterate through all 12 possible root notes
        # Rotate the templates to match the current root note
        major_rotated = np.roll(major_template, i)
        minor_rotated = np.roll(minor_template, i)

        # Calculate correlation (dot product, as vectors are normalized)
        corr_major = np.dot(avg_chroma, major_rotated)
        corr_minor = np.dot(avg_chroma, minor_rotated)

        key_candidates.append({'key': keys[i] + ' Major', 'correlation': corr_major})
        key_candidates.append({'key': keys[i] + ' Minor', 'correlation': corr_minor})

    # Find the key with the highest correlation
    best_match = max(key_candidates, key=lambda x: x['correlation'])
    return best_match['key']

estimated_key = estimate_key_from_chroma(avg_chroma, sr_features)
print(f"Estimated Key: {estimated_key}")

# Chroma visualization (optional, helps understand pitch content)
plt.figure(figsize=(12, 4))
librosa.display.specshow(chroma_harmonic, sr=sr_features, x_axis='time', y_axis='chroma', cmap='Greys')
plt.colorbar()
plt.title('Chromagram (Harmonic)')
plt.tight_layout()
plt.show()

print("\n--- Analysis Complete ---")