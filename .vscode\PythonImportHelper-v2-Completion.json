[{"label": "runpy", "kind": 6, "isExtraImport": true, "importPath": "runpy", "description": "runpy", "detail": "runpy", "documentation": {}}, {"label": "annotations", "importPath": "__future__", "description": "__future__", "isExtraImport": true, "detail": "__future__", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "librosa", "kind": 6, "isExtraImport": true, "importPath": "librosa", "description": "librosa", "detail": "librosa", "documentation": {}}, {"label": "librosa.display", "kind": 6, "isExtraImport": true, "importPath": "librosa.display", "description": "librosa.display", "detail": "librosa.display", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "bin_dir", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "bin_dir = os.path.dirname(abs_file)\nbase = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"audio\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "base", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "base = bin_dir[: -len(\"Scripts\") - 1]  # strip away the bin part from the __file__, plus the path separator\n# prepend bin to PATH (this file is inside the bin directory)\nos.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"audio\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"PATH\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"PATH\"] = os.pathsep.join([bin_dir, *os.environ.get(\"PATH\", \"\").split(os.pathsep)])\nos.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"audio\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV\"] = base  # virtual env is right above bin directory\nos.environ[\"VIRTUAL_ENV_PROMPT\"] = \"audio\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "os.environ[\"VIRTUAL_ENV_PROMPT\"]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "os.environ[\"VIRTUAL_ENV_PROMPT\"] = \"audio\" or os.path.basename(base)  # noqa: SIM222\n# add the virtual environments libraries to the host python import mechanism\nprev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "prev_length", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "prev_length = len(sys.path)\nfor lib in \"..\\\\Lib\\\\site-packages\".split(os.pathsep):\n    path = os.path.realpath(os.path.join(bin_dir, lib))\n    site.addsitedir(path)\nsys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.path[:]", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.path[:] = sys.path[prev_length:] + sys.path[0:prev_length]\nsys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.real_prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.real_prefix = sys.prefix\nsys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "sys.prefix", "kind": 5, "importPath": ".venv.Scripts.activate_this", "description": ".venv.Scripts.activate_this", "peekOfCode": "sys.prefix = base", "detail": ".venv.Scripts.activate_this", "documentation": {}}, {"label": "estimate_key_from_chroma", "kind": 2, "importPath": "main", "description": "main", "peekOfCode": "def estimate_key_from_chroma(avg_chroma, sr_features):\n    keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']\n    key_candidates = []\n    for i in range(12): # Iterate through all 12 possible root notes\n        # Rotate the templates to match the current root note\n        major_rotated = np.roll(major_template, i)\n        minor_rotated = np.roll(minor_template, i)\n        # Calculate correlation (dot product, as vectors are normalized)\n        corr_major = np.dot(avg_chroma, major_rotated)\n        corr_minor = np.dot(avg_chroma, minor_rotated)", "detail": "main", "documentation": {}}, {"label": "AUDIO_FILE", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "AUDIO_FILE = 'bura_din.mp3'\n# --- 0. Pre-check and Load Audio ---\nprint(f\"--- Starting Audio Analysis for: {AUDIO_FILE} ---\")\nif not os.path.exists(AUDIO_FILE):\n    print(f\"Error: Audio file not found at '{AUDIO_FILE}'.\")\n    print(\"Please ensure the file exists and the path is correct.\")\n    exit()\ntry:\n    # y: audio time series, sr: sampling rate of y\n    # Load at original sampling rate for accurate waveform/spectrograms", "detail": "main", "documentation": {}}, {"label": "rms", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "rms = librosa.feature.rms(y=y)[0]\ntimes = librosa.times_like(rms, sr=sr)\nplt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot\nplt.plot(times, rms, label='RMS Energy', color='teal')\nplt.title('RMS Energy Over Time (Loudness Indicator)')\nplt.xlabel('Time (s)')\nplt.ylabel('RMS Value')\nplt.grid(True)\nplt.legend()\nplt.tight_layout()", "detail": "main", "documentation": {}}, {"label": "times", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "times = librosa.times_like(rms, sr=sr)\nplt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot\nplt.plot(times, rms, label='RMS Energy', color='teal')\nplt.title('RMS Energy Over Time (Loudness Indicator)')\nplt.xlabel('Time (s)')\nplt.ylabel('RMS Value')\nplt.grid(True)\nplt.legend()\nplt.tight_layout()\nplt.show()", "detail": "main", "documentation": {}}, {"label": "avg_rms", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "avg_rms = np.mean(rms)\nprint(f\"Average RMS Energy: {avg_rms:.4f}\")\n# --- 2. Frequency Analysis: Spectrograms ---\nprint(\"\\n--- 2. Frequency Analysis (Spectrograms) ---\")\n# Compute Spectrogram (Short-Time Fourier Transform)\nD = librosa.stft(y)\nS_db = librosa.amplitude_to_db(np.abs(D), ref=np.max)\nplt.figure(figsize=(12, 10))\nplt.subplot(2, 1, 1) # 2 rows, 1 column, 1st plot\nlibrosa.display.specshow(S_db, sr=sr, x_axis='time', y_axis='hz', cmap='viridis')", "detail": "main", "documentation": {}}, {"label": "D", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "D = librosa.stft(y)\nS_db = librosa.amplitude_to_db(np.abs(D), ref=np.max)\nplt.figure(figsize=(12, 10))\nplt.subplot(2, 1, 1) # 2 rows, 1 column, 1st plot\nlibrosa.display.specshow(S_db, sr=sr, x_axis='time', y_axis='hz', cmap='viridis')\nplt.colorbar(format='%+2.0f dB')\nplt.title('Spectrogram (Frequency vs. Time)')\nplt.xlabel('Time (s)')\nplt.ylabel('Frequency (Hz)')\n# Compute Mel-Spectrogram (more perceptually relevant)", "detail": "main", "documentation": {}}, {"label": "S_db", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "S_db = librosa.amplitude_to_db(np.abs(D), ref=np.max)\nplt.figure(figsize=(12, 10))\nplt.subplot(2, 1, 1) # 2 rows, 1 column, 1st plot\nlibrosa.display.specshow(S_db, sr=sr, x_axis='time', y_axis='hz', cmap='viridis')\nplt.colorbar(format='%+2.0f dB')\nplt.title('Spectrogram (Frequency vs. Time)')\nplt.xlabel('Time (s)')\nplt.ylabel('Frequency (Hz)')\n# Compute Mel-Spectrogram (more perceptually relevant)\nM = librosa.feature.melspectrogram(y=y, sr=sr)", "detail": "main", "documentation": {}}, {"label": "M", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "M = librosa.feature.melspectrogram(y=y, sr=sr)\nM_db = librosa.amplitude_to_db(M, ref=np.max)\nplt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot\nlibrosa.display.specshow(M_db, sr=sr, x_axis='time', y_axis='mel', cmap='magma')\nplt.colorbar(format='%+2.0f dB')\nplt.title('Mel-Spectrogram')\nplt.xlabel('Time (s)')\nplt.ylabel('Mel Frequency')\nplt.tight_layout()\nplt.show()", "detail": "main", "documentation": {}}, {"label": "M_db", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "M_db = librosa.amplitude_to_db(M, ref=np.max)\nplt.subplot(2, 1, 2) # 2 rows, 1 column, 2nd plot\nlibrosa.display.specshow(M_db, sr=sr, x_axis='time', y_axis='mel', cmap='magma')\nplt.colorbar(format='%+2.0f dB')\nplt.title('Mel-Spectrogram')\nplt.xlabel('Time (s)')\nplt.ylabel('Mel Frequency')\nplt.tight_layout()\nplt.show()\n# --- 3. Musical Feature Analysis: Tempo (BPM) and Key Detection ---", "detail": "main", "documentation": {}}, {"label": "sr_features", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "sr_features = 22050\ny_features, _ = librosa.load(AUDIO_FILE, sr=sr_features) # Load again with specific SR for features\n# Tempo (BPM) Estimation\ntempo, beats = librosa.beat.beat_track(y=y_features, sr=sr_features)\n# FIX: Access the first element of the tempo array\nprint(f\"Estimated Tempo: {tempo[0]:.2f} BPM\")\n# Key Detection\n# First, separate harmonic components for better key estimation\ny_harmonic, y_percussive = librosa.effects.hpss(y_features)\n# Compute Chroma features on the harmonic part", "detail": "main", "documentation": {}}, {"label": "chroma_harmonic", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "chroma_harmonic = librosa.feature.chroma_cqt(y=y_harmonic, sr=sr_features)\n# Define Major and Minor chord templates (simplified for demonstration)\n# These represent the relative strength of each pitch class in a given key\nmajor_template = np.array([6.35, 2.23, 3.48, 2.33, 4.38, 4.09, 2.52, 5.19, 2.39, 3.66, 2.29, 2.88])\nminor_template = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])\n# Normalize templates to sum to 1 for better correlation comparison\nmajor_template /= np.sum(major_template)\nminor_template /= np.sum(minor_template)\n# Calculate the average chroma vector for the track\navg_chroma = np.mean(chroma_harmonic, axis=1)", "detail": "main", "documentation": {}}, {"label": "major_template", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "major_template = np.array([6.35, 2.23, 3.48, 2.33, 4.38, 4.09, 2.52, 5.19, 2.39, 3.66, 2.29, 2.88])\nminor_template = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])\n# Normalize templates to sum to 1 for better correlation comparison\nmajor_template /= np.sum(major_template)\nminor_template /= np.sum(minor_template)\n# Calculate the average chroma vector for the track\navg_chroma = np.mean(chroma_harmonic, axis=1)\n# Normalize the average chroma vector\navg_chroma /= np.sum(avg_chroma)\n# Function to correlate chroma with key templates", "detail": "main", "documentation": {}}, {"label": "minor_template", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "minor_template = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])\n# Normalize templates to sum to 1 for better correlation comparison\nmajor_template /= np.sum(major_template)\nminor_template /= np.sum(minor_template)\n# Calculate the average chroma vector for the track\navg_chroma = np.mean(chroma_harmonic, axis=1)\n# Normalize the average chroma vector\navg_chroma /= np.sum(avg_chroma)\n# Function to correlate chroma with key templates\ndef estimate_key_from_chroma(avg_chroma, sr_features):", "detail": "main", "documentation": {}}, {"label": "avg_chroma", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "avg_chroma = np.mean(chroma_harmonic, axis=1)\n# Normalize the average chroma vector\navg_chroma /= np.sum(avg_chroma)\n# Function to correlate chroma with key templates\ndef estimate_key_from_chroma(avg_chroma, sr_features):\n    keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']\n    key_candidates = []\n    for i in range(12): # Iterate through all 12 possible root notes\n        # Rotate the templates to match the current root note\n        major_rotated = np.roll(major_template, i)", "detail": "main", "documentation": {}}, {"label": "estimated_key", "kind": 5, "importPath": "main", "description": "main", "peekOfCode": "estimated_key = estimate_key_from_chroma(avg_chroma, sr_features)\nprint(f\"Estimated Key: {estimated_key}\")\n# Chroma visualization (optional, helps understand pitch content)\nplt.figure(figsize=(12, 4))\nlibrosa.display.specshow(chroma_harmonic, sr=sr_features, x_axis='time', y_axis='chroma', cmap='Greys')\nplt.colorbar()\nplt.title('Chromagram (Harmonic)')\nplt.tight_layout()\nplt.show()\nprint(\"\\n--- Analysis Complete ---\")", "detail": "main", "documentation": {}}]