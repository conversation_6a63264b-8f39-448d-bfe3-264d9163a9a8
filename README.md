# Appwrite Deployment for taskhub.me

This repository contains the configuration files to deploy Appwrite on your Azure VPS with the domain `taskhub.me`.

## 🌐 Server Information
- **Domain**: taskhub.me
- **Server IP**: *************
- **SSL**: Automatic Let's Encrypt certificates
- **Console**: https://taskhub.me/console

## 📁 Files Overview
- `docker-compose.yml` - Main Appwrite services configuration
- `.env` - Environment variables and configuration
- `deploy.sh` - Linux deployment script
- `deploy.ps1` - Windows PowerShell preparation script

## 🚀 Quick Deployment

### Step 1: Prepare Files (Windows)
Run the PowerShell script to generate secure passwords:
```powershell
.\deploy.ps1
```

### Step 2: Upload to VPS
Upload the files to your Azure VPS:
```bash
scp docker-compose.yml username@*************:~/
scp .env username@*************:~/
scp deploy.sh username@*************:~/
```

### Step 3: Deploy on VPS
Connect to your VPS and run the deployment:
```bash
ssh username@*************
chmod +x deploy.sh
sudo ./deploy.sh
```

### Step 4: Configure DNS
Point your domain to the server:
- A record: `@` → `*************`
- A record: `www` → `*************`

## ⚙️ Configuration Details

### Domain Configuration
- Main app: `https://taskhub.me`
- Console: `https://taskhub.me/console`
- Functions: `functions.taskhub.me`
- Sites: `sites.taskhub.me`

### SSL Certificates
- Automatic Let's Encrypt certificates
- HTTP to HTTPS redirect enabled
- Certificate email: `<EMAIL>`

### Security Features
- Strong random passwords generated automatically
- HTTPS enforced
- Firewall configured (ports 80, 443, SSH)

## 📧 Email Configuration (Optional)

To enable email functionality, update these variables in `.env`:
```env
_APP_SMTP_HOST=your-smtp-server.com
_APP_SMTP_PORT=587
_APP_SMTP_SECURE=tls
_APP_SMTP_USERNAME=<EMAIL>
_APP_SMTP_PASSWORD=your-email-password
```

## 🔧 Management Commands

### View logs
```bash
cd /opt/appwrite
docker-compose logs -f
```

### Restart services
```bash
cd /opt/appwrite
docker-compose restart
```

### Stop services
```bash
cd /opt/appwrite
docker-compose down
```

### Update Appwrite
```bash
cd /opt/appwrite
docker-compose pull
docker-compose up -d
```

## 🛠️ Troubleshooting

### Check service status
```bash
cd /opt/appwrite
docker-compose ps
```

### View specific service logs
```bash
cd /opt/appwrite
docker-compose logs appwrite
docker-compose logs traefik
```

### SSL Certificate Issues
If SSL certificates don't generate:
1. Ensure DNS is pointing to your server
2. Check if ports 80 and 443 are open
3. Wait up to 10 minutes for certificate generation

### Domain Not Working
1. Verify DNS propagation: `nslookup taskhub.me`
2. Check firewall: `sudo ufw status`
3. Verify Traefik is running: `docker-compose ps traefik`

## 📋 Post-Deployment Checklist

- [ ] DNS configured and propagated
- [ ] SSL certificates generated
- [ ] Console accessible at https://taskhub.me/console
- [ ] First project created
- [ ] Email settings configured (if needed)
- [ ] Backup strategy implemented

## 🔐 Security Notes

- All passwords are automatically generated and secure
- Keep the `appwrite-passwords.txt` file secure
- Regularly update Appwrite using the update commands
- Monitor logs for any security issues

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Review the logs using the management commands
3. Ensure all prerequisites are met
4. Verify DNS and firewall configuration