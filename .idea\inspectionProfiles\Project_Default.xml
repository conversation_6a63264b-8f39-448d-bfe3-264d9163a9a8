<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="17">
            <item index="0" class="java.lang.String" itemvalue="langgraph" />
            <item index="1" class="java.lang.String" itemvalue="transformers" />
            <item index="2" class="java.lang.String" itemvalue="pydantic" />
            <item index="3" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="4" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="5" class="java.lang.String" itemvalue="nltk" />
            <item index="6" class="java.lang.String" itemvalue="ollama" />
            <item index="7" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="8" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="9" class="java.lang.String" itemvalue="torch" />
            <item index="10" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="11" class="java.lang.String" itemvalue="numpy" />
            <item index="12" class="java.lang.String" itemvalue="pandas" />
            <item index="13" class="java.lang.String" itemvalue="pdfminer.six" />
            <item index="14" class="java.lang.String" itemvalue="langchain" />
            <item index="15" class="java.lang.String" itemvalue="unstructured" />
            <item index="16" class="java.lang.String" itemvalue="langchain-core" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>