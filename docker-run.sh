#!/bin/bash

# Simple Docker Run Script for Appwrite
# Domain: taskhub.me
# IP: *************

echo "🚀 Starting Appwrite with Docker Run commands..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create networks
print_status "Creating Docker networks..."
docker network create gateway 2>/dev/null || true
docker network create appwrite 2>/dev/null || true
docker network create runtimes 2>/dev/null || true

# Create volumes
print_status "Creating Docker volumes..."
docker volume create appwrite-mariadb 2>/dev/null || true
docker volume create appwrite-redis 2>/dev/null || true
docker volume create appwrite-cache 2>/dev/null || true
docker volume create appwrite-uploads 2>/dev/null || true
docker volume create appwrite-imports 2>/dev/null || true
docker volume create appwrite-certificates 2>/dev/null || true
docker volume create appwrite-functions 2>/dev/null || true
docker volume create appwrite-sites 2>/dev/null || true
docker volume create appwrite-builds 2>/dev/null || true
docker volume create appwrite-config 2>/dev/null || true

# Stop and remove existing containers
print_status "Stopping existing containers..."
docker stop appwrite-mariadb appwrite-redis openruntimes-executor appwrite appwrite-console appwrite-realtime appwrite-traefik 2>/dev/null || true
docker rm appwrite-mariadb appwrite-redis openruntimes-executor appwrite appwrite-console appwrite-realtime appwrite-traefik 2>/dev/null || true

# Start MariaDB
print_status "Starting MariaDB..."
docker run -d \
  --name appwrite-mariadb \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-mariadb:/var/lib/mysql:rw \
  -e MYSQL_ROOT_PASSWORD=rootsecretpassword \
  -e MYSQL_DATABASE=appwrite \
  -e MYSQL_USER=user \
  -e MYSQL_PASSWORD=password \
  -e MARIADB_AUTO_UPGRADE=1 \
  mariadb:10.11 \
  mysqld --innodb-flush-method=fsync

# Start Redis
print_status "Starting Redis..."
docker run -d \
  --name appwrite-redis \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-redis:/data:rw \
  redis:7.2.4-alpine \
  redis-server --maxmemory 512mb --maxmemory-policy allkeys-lru --maxmemory-samples 5

# Wait for database
print_status "Waiting for database to be ready..."
sleep 20

# Start OpenRuntimes Executor
print_status "Starting OpenRuntimes Executor..."
docker run -d \
  --name openruntimes-executor \
  --hostname exc1 \
  --network appwrite \
  --network runtimes \
  --restart unless-stopped \
  --stop-signal SIGINT \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v appwrite-builds:/storage/builds:rw \
  -v appwrite-functions:/storage/functions:rw \
  -v appwrite-sites:/storage/sites:rw \
  -v /tmp:/tmp:rw \
  --env-file .env \
  openruntimes/executor:0.7.14

# Start Appwrite main service
print_status "Starting Appwrite main service..."
docker run -d \
  --name appwrite \
  --network appwrite \
  --restart unless-stopped \
  -v appwrite-uploads:/storage/uploads:rw \
  -v appwrite-imports:/storage/imports:rw \
  -v appwrite-cache:/storage/cache:rw \
  -v appwrite-config:/storage/config:rw \
  -v appwrite-certificates:/storage/certificates:rw \
  -v appwrite-functions:/storage/functions:rw \
  -v appwrite-sites:/storage/sites:rw \
  -v appwrite-builds:/storage/builds:rw \
  --env-file .env \
  -l "traefik.enable=true" \
  -l "traefik.constraint-label-stack=appwrite" \
  -l "traefik.docker.network=appwrite" \
  -l "traefik.http.services.appwrite_api.loadbalancer.server.port=80" \
  -l "traefik.http.routers.appwrite_api_http.entrypoints=appwrite_web" \
  -l "traefik.http.routers.appwrite_api_http.rule=Host(\`taskhub.me\`) && PathPrefix(\`/\`)" \
  -l "traefik.http.routers.appwrite_api_http.service=appwrite_api" \
  -l "traefik.http.routers.appwrite_api_https.entrypoints=appwrite_websecure" \
  -l "traefik.http.routers.appwrite_api_https.rule=Host(\`taskhub.me\`) && PathPrefix(\`/\`)" \
  -l "traefik.http.routers.appwrite_api_https.service=appwrite_api" \
  -l "traefik.http.routers.appwrite_api_https.tls=true" \
  -l "traefik.http.routers.appwrite_api_https.tls.certresolver=letsencrypt" \
  appwrite/appwrite:1.7.3

# Start Appwrite Console
print_status "Starting Appwrite Console..."
docker run -d \
  --name appwrite-console \
  --network appwrite \
  --restart unless-stopped \
  -l "traefik.enable=true" \
  -l "traefik.constraint-label-stack=appwrite" \
  -l "traefik.docker.network=appwrite" \
  -l "traefik.http.services.appwrite_console.loadbalancer.server.port=80" \
  -l "traefik.http.routers.appwrite_console_http.entrypoints=appwrite_web" \
  -l "traefik.http.routers.appwrite_console_http.rule=Host(\`taskhub.me\`) && PathPrefix(\`/console\`)" \
  -l "traefik.http.routers.appwrite_console_http.service=appwrite_console" \
  -l "traefik.http.routers.appwrite_console_https.entrypoints=appwrite_websecure" \
  -l "traefik.http.routers.appwrite_console_https.rule=Host(\`taskhub.me\`) && PathPrefix(\`/console\`)" \
  -l "traefik.http.routers.appwrite_console_https.service=appwrite_console" \
  -l "traefik.http.routers.appwrite_console_https.tls=true" \
  -l "traefik.http.routers.appwrite_console_https.tls.certresolver=letsencrypt" \
  appwrite/console:6.0.11

# Start Appwrite Realtime
print_status "Starting Appwrite Realtime..."
docker run -d \
  --name appwrite-realtime \
  --network appwrite \
  --restart unless-stopped \
  --env-file .env \
  -l "traefik.enable=true" \
  -l "traefik.constraint-label-stack=appwrite" \
  -l "traefik.docker.network=appwrite" \
  -l "traefik.http.services.appwrite_realtime.loadbalancer.server.port=80" \
  -l "traefik.http.routers.appwrite_realtime_ws.entrypoints=appwrite_web" \
  -l "traefik.http.routers.appwrite_realtime_ws.rule=Host(\`taskhub.me\`) && PathPrefix(\`/v1/realtime\`)" \
  -l "traefik.http.routers.appwrite_realtime_ws.service=appwrite_realtime" \
  -l "traefik.http.routers.appwrite_realtime_wss.entrypoints=appwrite_websecure" \
  -l "traefik.http.routers.appwrite_realtime_wss.rule=Host(\`taskhub.me\`) && PathPrefix(\`/v1/realtime\`)" \
  -l "traefik.http.routers.appwrite_realtime_wss.service=appwrite_realtime" \
  -l "traefik.http.routers.appwrite_realtime_wss.tls=true" \
  -l "traefik.http.routers.appwrite_realtime_wss.tls.certresolver=letsencrypt" \
  appwrite/appwrite:1.7.3 realtime

# Start Traefik
print_status "Starting Traefik reverse proxy..."
docker run -d \
  --name appwrite-traefik \
  --network gateway \
  --restart unless-stopped \
  -p 80:80 \
  -p 443:443 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v appwrite-config:/storage/config:ro \
  -v appwrite-certificates:/storage/certificates:ro \
  traefik:2.11 \
  --providers.file.directory=/storage/config \
  --providers.file.watch=true \
  --providers.docker=true \
  --providers.docker.exposedByDefault=false \
  --providers.docker.constraints=Label\(\`traefik.constraint-label-stack\`,\`appwrite\`\) \
  --entrypoints.appwrite_web.address=:80 \
  --entrypoints.appwrite_websecure.address=:443 \
  --certificatesresolvers.letsencrypt.acme.httpchallenge=true \
  --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=appwrite_web \
  --certificatesresolvers.letsencrypt.acme.email=<EMAIL> \
  --certificatesresolvers.letsencrypt.acme.storage=/storage/acme.json \
  --entrypoints.appwrite_web.http.redirections.entrypoint.to=appwrite_websecure \
  --entrypoints.appwrite_web.http.redirections.entrypoint.scheme=https

# Connect Traefik to appwrite network
docker network connect appwrite appwrite-traefik

print_status "Waiting for services to start..."
sleep 30

print_status "Checking service status..."
docker ps --filter "name=appwrite"

echo ""
echo "🎉 Appwrite deployment with Docker Run completed!"
echo ""
echo "📋 Access Information:"
echo "🌐 Main site: https://taskhub.me"
echo "🖥️  Console: https://taskhub.me/console"
echo ""
echo "🔧 Management Commands:"
echo "View logs: docker logs -f appwrite"
echo "Restart: docker restart appwrite"
echo "Stop all: docker stop \$(docker ps -q --filter 'name=appwrite')"
